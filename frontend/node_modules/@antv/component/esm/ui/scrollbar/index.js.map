{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/ui/scrollbar/index.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,WAAW,EAAE,MAAM,SAAS,CAAC;AACtC,OAAO,EAAE,KAAK,EAAE,MAAM,YAAY,CAAC;AACnC,OAAO,EAAE,SAAS,EAAE,MAAM,YAAY,CAAC;AAEvC,OAAO,EAAE,WAAW,EAAE,eAAe,EAAE,MAAM,EAAE,aAAa,EAAE,eAAe,EAAE,MAAM,YAAY,CAAC;AAElG,OAAO,EAAE,MAAM,EAAE,MAAM,WAAW,CAAC;AAKnC;IAA+B,6BAA8B;IAY3D,mBAAY,OAAyB;QACnC,YAAA,MAAK,YAAC,OAAO,EAAE;YACb,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,CAAC;YACJ,OAAO,EAAE,IAAI;YACb,WAAW,EAAE,UAAU;YACvB,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;YACrB,UAAU,EAAE,IAAI;YAChB,QAAQ,EAAE,IAAI;YACd,WAAW,EAAE,SAAS;YACtB,SAAS,EAAE,EAAE;YACb,KAAK,EAAE,CAAC;SACT,CAAC,SAAC;QAnBG,WAAK,GAAqB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QA0HzC;;WAEG;QACK,mBAAa,GAAG,UAAC,QAAa;YAC5B,IAAO,QAAQ,GAAK,KAAI,CAAC,UAAU,MAApB,CAAqB;YAC5C,IAAI,QAAQ,KAAK,QAAQ;gBAAE,OAAO;YAClC,IAAM,MAAM,GAAG;gBACb,MAAM,EAAE;oBACN,QAAQ,UAAA;oBACR,KAAK,EAAE,QAAQ;iBAChB;aACF,CAAC;YACF,KAAI,CAAC,aAAa,CAAC,IAAI,WAAW,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC;YACtD,KAAI,CAAC,aAAa,CAAC,IAAI,WAAW,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC,CAAC;QAC7D,CAAC,CAAC;QAmBF;;WAEG;QACK,kBAAY,GAAG,UAAC,CAAM;YACpB,IAAA,QAAQ,GAAK,KAAI,CAAC,UAAU,SAApB,CAAqB;YACrC,IAAI,CAAC,QAAQ;gBAAE,OAAO;YAChB,IAAA,KAAA,OAAS,KAAI,CAAC,gBAAgB,EAAE,IAAA,EAA/B,CAAC,QAAA,EAAE,CAAC,QAA2B,CAAC;YACjC,IAAA,KAAA,OAAkB,KAAI,CAAC,OAAO,IAAA,EAA7B,GAAG,QAAA,EAAM,IAAI,QAAgB,CAAC;YACrC,IAAM,OAAO,GAAG,KAAI,CAAC,YAAY,CAAC,CAAE,CAAY,GAAG,IAAI,EAAG,CAAY,GAAG,GAAG,CAAC,CAAC,CAAC;YAC/E,IAAM,QAAQ,GAAG,KAAI,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;YACnD,IAAM,KAAK,GAAG,CAAC,QAAQ,GAAG,OAAO,CAAC,GAAG,KAAI,CAAC,WAAW,CAAC;YACtD,KAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QAC7B,CAAC,CAAC;QAYM,uBAAiB,GAAG,UAAC,CAAc;YACzC,KAAI,CAAC,aAAa,CAAC,IAAI,WAAW,CAAC,iBAAiB,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QAC/E,CAAC,CAAC;QAEM,uBAAiB,GAAG,UAAC,CAAc;YACzC,KAAI,CAAC,aAAa,CAAC,IAAI,WAAW,CAAC,iBAAiB,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QAC/E,CAAC,CAAC;QAEM,uBAAiB,GAAG,UAAC,CAAc;YACzC,KAAI,CAAC,aAAa,CAAC,IAAI,WAAW,CAAC,iBAAiB,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QAC/E,CAAC,CAAC;QAEM,uBAAiB,GAAG,UAAC,CAAc;YACzC,KAAI,CAAC,aAAa,CAAC,IAAI,WAAW,CAAC,iBAAiB,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QAC/E,CAAC,CAAC;;IA7KF,CAAC;IAlBD,sBAAY,8BAAO;aAAnB;YACU,IAAA,OAAO,GAAK,IAAI,CAAC,UAAU,QAApB,CAAqB;YACpC,OAAO,eAAe,CAAC,OAAO,CAAC,CAAC;QAClC,CAAC;;;OAAA;IAiBD,sBAAY,4BAAK;aAAjB;YACU,IAAA,KAAK,GAAK,IAAI,CAAC,UAAU,MAApB,CAAqB;YAC5B,IAAA,KAAA,OAAa,IAAI,CAAC,KAAK,IAAA,EAAtB,GAAG,QAAA,EAAE,GAAG,QAAc,CAAC;YAC9B,OAAO,KAAK,CAAC,KAAK,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QAChC,CAAC;;;OAAA;IAED,sBAAY,kCAAW;aAAvB;YACQ,IAAA,KAAmD,IAAI,CAAC,UAAU,EAAhE,cAAc,oBAAA,EAAE,mBAA4B,EAA5B,WAAW,mBAAG,cAAc,KAAoB,CAAC;YACzE,OAAO,WAAW,CAAC;QACrB,CAAC;;;OAAA;IAED,sBAAY,qCAAc;aAA1B;YACU,IAAA,SAAS,GAAK,IAAI,CAAC,UAAU,UAApB,CAAqB;YACtC,IAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;YAC/B,IAAA,KAAA,OAA6B,IAAI,CAAC,OAAO,IAAA,EAAxC,GAAG,QAAA,EAAE,KAAK,QAAA,EAAE,MAAM,QAAA,EAAE,IAAI,QAAgB,CAAC;YAC1C,IAAA,KAAA,OAAkB,IAAI,CAAC,YAAY,CAAC;gBACxC,CAAC,WAAW,EAAE,SAAS,CAAC;gBACxB,CAAC,SAAS,EAAE,WAAW,CAAC;aACzB,CAAC,IAAA,EAHK,KAAK,QAAA,EAAE,MAAM,QAGlB,CAAC;YACH,OAAO;gBACL,CAAC,EAAE,IAAI;gBACP,CAAC,EAAE,GAAG;gBACN,KAAK,EAAE,CAAC,KAAK,GAAG,CAAC,IAAI,GAAG,KAAK,CAAC;gBAC9B,MAAM,EAAE,CAAC,MAAM,GAAG,CAAC,GAAG,GAAG,MAAM,CAAC;aACjC,CAAC;QACJ,CAAC;;;OAAA;IAED,sBAAY,kCAAW;aAAvB;YACQ,IAAA,KAAyB,IAAI,CAAC,UAAU,EAAtC,OAAO,aAAA,EAAE,SAAS,eAAoB,CAAC;YAC/C,IAAI,CAAC,OAAO;gBAAE,OAAO,CAAC,CAAC;YACvB,OAAO,SAAS,GAAG,CAAC,CAAC;QACvB,CAAC;;;OAAA;IAED,sBAAY,kCAAW;aAAvB;YACQ,IAAA,KAA2B,IAAI,CAAC,UAAU,EAAxC,OAAO,aAAA,EAAE,WAAW,iBAAoB,CAAC;YACjD,IAAI,CAAC,OAAO;gBAAE,OAAO,CAAC,CAAC;YACjB,IAAA,KAAoB,IAAI,CAAC,cAAc,EAArC,KAAK,WAAA,EAAE,MAAM,YAAwB,CAAC;YAC9C,OAAO,WAAW,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;QAC/D,CAAC;;;OAAA;IAED;;OAEG;IACK,6BAAS,GAAjB,UAAkB,KAAkB;QAAlB,sBAAA,EAAA,QAAQ,IAAI,CAAC,KAAK;QAC5B,IAAA,KAAoC,IAAI,CAAC,UAAU,EAAjD,cAAc,oBAAA,EAAE,aAAa,mBAAoB,CAAC;QAC1D,IAAM,IAAI,GAAG,cAAc,GAAG,aAAa,CAAC;QACtC,IAAA,KAAA,OAAa,IAAI,CAAC,KAAK,IAAA,EAAtB,GAAG,QAAA,EAAE,GAAG,QAAc,CAAC;QAC9B,IAAM,KAAK,GAAG,KAAK,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC,CAAC;QACzC,OAAO,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI,CAAC,CAAC;IAC/B,CAAC;IAEM,4BAAQ,GAAf;QACE,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IAEO,gCAAY,GAApB,UAAqB,SAAgB;QAC7B,IAAA,KAAsD,IAAI,CAAC,UAAU,EAAnE,CAAC,OAAA,EAAE,CAAC,OAAA,EAAE,WAAW,iBAAA,EAAE,SAAS,eAAA,EAAE,OAAO,aAAA,EAAE,QAAQ,cAAoB,CAAC;QAC5E,IAAM,UAAU,GAAG,aAAa,CAAiB,IAAI,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QAC3E,IAAM,cAAc,GAAG,aAAa,CAAiB,IAAI,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QAE/E,IAAM,KAAK,uBACT,CAAC,GAAA,EACD,CAAC,GAAA,EACD,SAAS,EAAE,KAAK,EAChB,WAAW,aAAA,EACX,OAAO,SAAA,EACP,eAAe,EAAE,IAAI,CAAC,WAAW,EACjC,UAAU,EAAE,KAAK,EACjB,QAAQ,UAAA,EACR,WAAW,EAAE,IAAI,CAAC,WAAW,EAC7B,WAAW,EAAE,IAAI,CAAC,WAAW,EAC7B,SAAS,WAAA,EACT,MAAM,EAAE,IAAI,CAAC,SAAS,EAAE,IACrB,eAAe,CAAC,UAAU,EAAE,OAAO,CAAC,GACpC,eAAe,CAAC,cAAc,EAAE,WAAW,CAAC,CAChD,CAAC;QAEF,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC;aAC5B,sBAAsB,CAAC,WAAW,EAAE,cAAM,OAAA,IAAI,MAAM,CAAC,EAAE,KAAK,OAAA,EAAE,CAAC,EAArB,CAAqB,CAAC;aAChE,MAAM,CAAC,KAAK,CAAC;aACb,IAAI,EAAE,CAAC;IACZ,CAAC;IAEM,0BAAM,GAAb,UAAc,UAA+B,EAAE,SAAgB;QAC7D,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;IAC/B,CAAC;IAED;;;OAGG;IACI,4BAAQ,GAAf,UAAgB,KAAa,EAAE,OAAwB;QAAxB,wBAAA,EAAA,eAAwB;QAC7C,IAAO,QAAQ,GAAK,IAAI,CAAC,UAAU,MAApB,CAAqB;QACtC,IAAA,KAAA,OAAa,IAAI,CAAC,KAAK,IAAA,EAAtB,GAAG,QAAA,EAAE,GAAG,QAAc,CAAC;QAC9B,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;QACvE,kBAAkB;QAClB,mCAAmC;QACnC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;IAC/B,CAAC;IAkBM,8BAAU,GAAjB;QAAA,iBAMC;QALC,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,YAAY,EAAE,UAAC,CAAc;YACxD,CAAC,CAAC,eAAe,EAAE,CAAC;YACpB,KAAI,CAAC,YAAY,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,OAAO,EAAE,CAAC;IACjB,CAAC;IAED;;;OAGG;IACK,gCAAY,GAApB,UAAwB,MAAc;QAC5B,IAAA,WAAW,GAAK,IAAI,CAAC,UAAU,YAApB,CAAqB;QACxC,OAAO,WAAW,KAAK,YAAY,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IAC9D,CAAC;IAgBD;;OAEG;IACK,2BAAO,GAAf;QACE,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,qBAAqB,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAC5E,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;QACxE,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,qBAAqB,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAC5E,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;IAC1E,CAAC;IArLa,aAAG,GAAG,WAAW,AAAd,CAAe;IAsMlC,gBAAC;CAAA,AAvMD,CAA+B,SAAS,GAuMvC;SAvMY,SAAS"}