/**
 * 尺寸配置
 */
export var SIZE_STYLE = {
    small: {
        textFontSize: 10,
        buttonWidth: 40,
        buttonHeight: 20,
        markerSize: 8,
    },
    middle: {
        textFontSize: 12,
        buttonWidth: 60,
        buttonHeight: 30,
        markerSize: 12,
    },
    large: {
        textFontSize: 16,
        buttonWidth: 80,
        buttonHeight: 40,
        markerSize: 16,
    },
};
/**
 * 类型配置
 */
export var TYPE_STYLE = {
    primary: {
        default: { textFill: '#fff', buttonFill: '#1890ff', buttonLineWidth: 0, markerFill: '#1890ff' },
        active: {
            buttonFill: '#40a9ff',
            markerFill: '#40a9ff',
        },
        disabled: {},
    },
    dashed: {
        default: {
            buttonFill: 'transparent',
            buttonStroke: '#bbb',
            buttonLineDash: [5, 5],
        },
        active: {},
        disabled: {},
    },
    link: {
        default: { textFill: '#1890ff', buttonFill: 'transparent', buttonLineWidth: 0, markerFill: '#1890ff' },
        active: {},
        disabled: {},
    },
    text: {
        default: { textFill: '#000', buttonFill: 'transparent', buttonLineWidth: 0, markerFill: '#000' },
        active: {},
        disabled: {},
    },
    default: {
        default: { textFill: '#000', buttonFill: 'transparent', buttonStroke: '#bbb', markerFill: '#bbb' },
        active: { textFill: '#1890ff', buttonStroke: '#1890ff', markerStroke: '#1890ff' },
        disabled: {},
    },
};
/**
 * disabled style
 */
export var DISABLED_STYLE = {
    // 严格需要替换的样式
    strict: {
        textFill: '#b8b8b8',
    },
    buttonStroke: '#d9d9d9',
    buttonFill: '#f5f5f5',
    markerStroke: '#d9d9d9',
};
//# sourceMappingURL=constant.js.map