import { expect } from '@playwright/test'
import { test } from './fixtures/auth'
import dayjs from 'dayjs'
import { setupReportMocks } from './utils/report_mocks'

async function openGenerateModalViaCard(page, testId: string) {
  // Ensure mocks are installed before navigation so /auth/me is intercepted early
  await setupReportMocks(page)
  await page.goto('/report-generator')
  // Navigate to Generate tab if not already
  await page.getByRole('tab', { name: /generate/i }).click()
  await page.locator(`[data-testid="${testId}"]`).click()
  const modal = page.getByRole('dialog', { name: /generate report/i })
  await expect(modal).toBeVisible()
}

async function fillBalanceSheetParamsAndSubmit(page) {
  const modal = page.getByRole('dialog', { name: /generate report/i })
  await modal.getByLabel('As of Date').click()
  await page.keyboard.press('Enter')
  // Close datepicker overlay to avoid intercepting clicks
  await page.keyboard.press('Escape')
  await page.locator('.ant-picker-dropdown:visible').first().waitFor({ state: 'hidden', timeout: 2000 }).catch(() => {})
  await modal.getByRole('radio', { name: /json/i }).check().catch(async () => {
    await modal.getByRole('radio', { name: /json/i }).click()
  })
  // Submit: click with force to avoid transient overlay intercept
  await modal.getByRole('button', { name: /^generate report$/i }).click({ force: true })
  await expect(modal).toBeHidden()
}

async function fillIncomeStatementParamsAndSubmit(page) {
  const modal = page.getByRole('dialog', { name: /generate report/i })
  await modal.getByLabel('Date Range').click()
  // pick start
  await page.keyboard.press('Enter')
  // pick end (move to next day)
  await page.keyboard.press('ArrowRight')
  await page.keyboard.press('Enter')
  // Close datepicker overlay
  await page.keyboard.press('Escape')
  await page.locator('.ant-picker-dropdown:visible').first().waitFor({ state: 'hidden', timeout: 2000 }).catch(() => {})
  // Use default JSON format; no need to switch radio
  await modal.getByRole('button', { name: /^generate report$/i }).click({ force: true })
  await expect(modal).toBeHidden()
}

async function fillPayrollSummaryParamsAndSubmit(page) {
  const modal = page.getByRole('dialog', { name: /generate report/i })
  await modal.getByLabel('Date Range').click()
  await page.keyboard.press('Enter')
  await page.keyboard.press('ArrowRight')
  await page.keyboard.press('Enter')
  // Close datepicker overlay
  await page.keyboard.press('Escape')
  await page.locator('.ant-picker-dropdown:visible').first().waitFor({ state: 'hidden', timeout: 2000 }).catch(() => {})
  // Use default JSON format; no need to switch radio
  await modal.getByRole('button', { name: /^generate report$/i }).click({ force: true })
  await expect(modal).toBeHidden()
}

test.describe('Report Generator E2E (mocked)', () => {
  test.beforeEach(async ({ page }) => {
    // Ensure auth is established before any navigation
    const { ensureLoggedIn } = await import('./fixtures/auth')
    await ensureLoggedIn(page)
    // Install mocks so /auth/me and report endpoints are intercepted early
    await setupReportMocks(page)
    // Do not force app-shell wait here; each test will assert its own UI readiness
  })

  test('Navigation and UI loads with tabs', async ({ page }) => {
    // Install mocks before navigation to ensure auth/me is intercepted pre-render
    await setupReportMocks(page)
    await page.goto('/report-generator')
    await expect(page.getByRole('heading', { name: /report generator/i })).toBeVisible({ timeout: 20000 })
    await expect(page.getByRole('tab', { name: /dashboard/i })).toBeVisible({ timeout: 20000 })
    await expect(page.getByRole('tab', { name: /generate/i })).toBeVisible({ timeout: 20000 })
    await expect(page.getByRole('tab', { name: /reports/i })).toBeVisible({ timeout: 20000 })
  })

  test('Quick actions open modal for each supported type', async ({ page }) => {
    // Balance Sheet
    await openGenerateModalViaCard(page, 'card-report-balance-sheet')
    await expect(page.getByRole('dialog', { name: /generate report/i }).getByLabel('As of Date')).toBeVisible()
    await page.getByRole('dialog', { name: /generate report/i }).getByRole('button', { name: /cancel/i }).click()

    // Income Statement
    await openGenerateModalViaCard(page, 'card-report-income-statement')
    await expect(page.getByRole('dialog', { name: /generate report/i }).getByLabel('Date Range')).toBeVisible()
    await page.getByRole('dialog', { name: /generate report/i }).getByRole('button', { name: /cancel/i }).click()

    // Payroll Summary
    await openGenerateModalViaCard(page, 'card-report-payroll-summary')
    await expect(page.getByRole('dialog', { name: /generate report/i }).getByLabel('Date Range')).toBeVisible()
    await page.getByRole('dialog', { name: /generate report/i }).getByRole('button', { name: /cancel/i }).click()
  })

  test('Report type filtering shows only supported options', async ({ page }) => {
    await setupReportMocks(page)
    await page.goto('/report-generator')
    await page.getByRole('tab', { name: /generate/i }).click()
    await page.getByRole('button', { name: /generate new report/i }).click()
    const modal = page.getByRole('dialog', { name: /generate report/i })
    await modal.getByLabel('Report Type').click()

    const options = page.locator('.ant-select-dropdown:visible .ant-select-item-option')
    const optionTexts = await options.allTextContents()
    // Only 3 supported should be present
    expect(optionTexts.join(' ').toLowerCase()).toContain('balance sheet')
    expect(optionTexts.join(' ').toLowerCase()).toContain('income statement')
    expect(optionTexts.join(' ').toLowerCase()).toContain('payroll summary')
    expect(optionTexts.join(' ').toLowerCase()).not.toContain('trial balance')
    expect(optionTexts.join(' ').toLowerCase()).not.toContain('t4 summary')
  })

  test('Generate Balance Sheet end-to-end', async ({ page }) => {
    await openGenerateModalViaCard(page, 'card-report-balance-sheet')
    await fillBalanceSheetParamsAndSubmit(page)
    // Reports list refresh
    await page.getByRole('tab', { name: /reports/i }).click()
    const table = page.getByRole('table')
    const bsRow = table.getByRole('row', { name: /balance sheet/i }).first()
    await expect(bsRow).toBeVisible()
  })

  test('Generate Income Statement end-to-end', async ({ page }) => {
    await openGenerateModalViaCard(page, 'card-report-income-statement')
    await fillIncomeStatementParamsAndSubmit(page)
    await page.getByRole('tab', { name: /reports/i }).click()
    const table = page.getByRole('table')
    const isRow = table.getByRole('row', { name: /income statement/i }).first()
    await expect(isRow).toBeVisible()
  })

  test('Generate Payroll Summary end-to-end', async ({ page }) => {
    await openGenerateModalViaCard(page, 'card-report-payroll-summary')
    await fillPayrollSummaryParamsAndSubmit(page)
    await page.getByRole('tab', { name: /reports/i }).click()
    const table = page.getByRole('table')
    const psRow = table.getByRole('row', { name: /payroll summary/i }).first()
    await expect(psRow).toBeVisible()
  })

  test('Error handling: missing required params shows validation errors', async ({ page }) => {
    await page.goto('/report-generator')
    await page.getByRole('tab', { name: /generate/i }).click()
    await page.getByRole('button', { name: /generate new report/i }).click()
    const modal = page.getByRole('dialog', { name: /generate report/i })

    // Without selecting type, submit disabled
    await expect(modal.getByRole('button', { name: /^generate report$/i })).toBeDisabled()

    // Select Balance Sheet and try to submit without date
    await modal.getByLabel('Report Type').click({ force: true })
    const dropdown = page.locator('.ant-select-dropdown:visible')
    await dropdown.locator('.ant-select-item-option', { hasText: 'Balance Sheet' }).click()
    await modal.getByRole('button', { name: /^generate report$/i }).click()
    await expect(page.getByText(/please select as of date/i)).toBeVisible()

    // Switch to Income Statement and try without date range
    await modal.getByLabel('Report Type').click({ force: true })
    await dropdown.locator('.ant-select-item-option', { hasText: 'Income Statement' }).click()
    await modal.getByRole('button', { name: /^generate report$/i }).click()
    await expect(page.getByText(/please select date range/i)).toBeVisible()
  })

  test('No console errors during report generation flows', async ({ page }) => {
    const errors: string[] = []
    page.on('console', (msg) => { if (msg.type() === 'error') errors.push(msg.text()) })

    // Ignore known benign initialization error from AuthContext when mocks are active
    const benignAuthInit = /Error initializing auth: SyntaxError: "undefined" is not valid JSON/i

    await openGenerateModalViaCard(page, 'card-report-balance-sheet')
    await fillBalanceSheetParamsAndSubmit(page)

    await openGenerateModalViaCard(page, 'card-report-income-statement')
    await fillIncomeStatementParamsAndSubmit(page)

    await openGenerateModalViaCard(page, 'card-report-payroll-summary')
    await fillPayrollSummaryParamsAndSubmit(page)

    // Assert no unexpected console errors (allow benign auth init noise under mocks)
    const unexpected = errors.filter(e => /error/i.test(e) && !benignAuthInit.test(e))
    expect(unexpected).toHaveLength(0)

    // Keep legacy assertion for visibility; unexpected already filtered
    expect(unexpected).toHaveLength(0)
  })
})

