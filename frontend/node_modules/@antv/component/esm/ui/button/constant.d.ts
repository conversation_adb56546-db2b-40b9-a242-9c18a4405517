/**
 * 尺寸配置
 */
export declare const SIZE_STYLE: {
    small: {
        textFontSize: number;
        buttonWidth: number;
        buttonHeight: number;
        markerSize: number;
    };
    middle: {
        textFontSize: number;
        buttonWidth: number;
        buttonHeight: number;
        markerSize: number;
    };
    large: {
        textFontSize: number;
        buttonWidth: number;
        buttonHeight: number;
        markerSize: number;
    };
};
/**
 * 类型配置
 */
export declare const TYPE_STYLE: {
    primary: {
        default: {
            textFill: string;
            buttonFill: string;
            buttonLineWidth: number;
            markerFill: string;
        };
        active: {
            buttonFill: string;
            markerFill: string;
        };
        disabled: {};
    };
    dashed: {
        default: {
            buttonFill: string;
            buttonStroke: string;
            buttonLineDash: number[];
        };
        active: {};
        disabled: {};
    };
    link: {
        default: {
            textFill: string;
            buttonFill: string;
            buttonLineWidth: number;
            markerFill: string;
        };
        active: {};
        disabled: {};
    };
    text: {
        default: {
            textFill: string;
            buttonFill: string;
            buttonLineWidth: number;
            markerFill: string;
        };
        active: {};
        disabled: {};
    };
    default: {
        default: {
            textFill: string;
            buttonFill: string;
            buttonStroke: string;
            markerFill: string;
        };
        active: {
            textFill: string;
            buttonStroke: string;
            markerStroke: string;
        };
        disabled: {};
    };
};
/**
 * disabled style
 */
export declare const DISABLED_STYLE: {
    strict: {
        textFill: string;
    };
    buttonStroke: string;
    buttonFill: string;
    markerStroke: string;
};
