{"version": 3, "file": "axis.js", "sourceRoot": "", "sources": ["../../../src/ui/axis/axis.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,oBAAoB,EAAE,MAAM,iBAAiB,CAAC;AACvD,OAAO,EAAE,SAAS,EAAE,MAAM,YAAY,CAAC;AAGvC,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,YAAY,CAAC;AACtD,OAAO,EAAE,sBAAsB,EAAE,WAAW,EAAE,MAAM,YAAY,CAAC;AACjE,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAC3C,OAAO,EAAE,YAAY,EAAE,MAAM,iBAAiB,CAAC;AAC/C,OAAO,EAAE,cAAc,EAAE,MAAM,eAAe,CAAC;AAC/C,OAAO,EAAE,WAAW,EAAE,MAAM,gBAAgB,CAAC;AAC7C,OAAO,EAAE,WAAW,EAAE,MAAM,gBAAgB,CAAC;AAY7C,SAAS,cAAc,CACrB,UAAkC,EAClC,SAAoB,EACpB,IAAiB,EACjB,SAAkC;IAE1B,IAAA,QAAQ,GAA0B,UAAU,SAApC,EAAE,QAAQ,GAAgB,UAAU,SAA1B,EAAE,SAAS,GAAK,UAAU,UAAf,CAAgB;IACrD,WAAW;IACX,IAAM,SAAS,GAAG,SAAS,CAAC,sBAAsB,CAAC,WAAW,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;IAC/E,IAAM,eAAe,GACnB,MAAM,CAAC,QAAS,EAAE,SAAS,EAAE,UAAC,KAAK;QACjC,OAAO,cAAc,CAAC,KAAK,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;IACtD,CAAC,CAAC,IAAI,EAAE,CAAC;IAEX,WAAW;IACX,IAAM,SAAS,GAAG,SAAS,CAAC,sBAAsB,CAAC,WAAW,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;IAC/E,IAAM,eAAe,GACnB,MAAM,CAAC,QAAS,EAAE,SAAS,EAAE,UAAC,KAAK;QACjC,OAAO,WAAW,CAAC,KAAK,EAAE,IAAI,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;IACzD,CAAC,CAAC,IAAI,EAAE,CAAC;IAEX,YAAY;IACZ,IAAM,UAAU,GAAG,SAAS,CAAC,sBAAsB,CAAC,WAAW,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;IACjF,IAAM,gBAAgB,GACpB,MAAM,CAAC,SAAU,EAAE,UAAU,EAAE,UAAC,KAAK;QACnC,OAAO,YAAY,CAAC,KAAK,EAAE,IAAI,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC;IAC5E,CAAC,CAAC,IAAI,EAAE,CAAC;IAEX,OAAO,qDAAI,eAAe,kBAAK,eAAe,kBAAK,gBAAgB,UAAE,MAAM,CAAC,UAAC,CAAC,IAAK,OAAA,CAAC,CAAC,CAAC,EAAH,CAAG,CAAC,CAAC;AAC1F,CAAC;AAED;IAA0B,wBAAyB;IACjD,cAAY,OAAoB;QAC9B,OAAA,MAAK,YAAC,OAAO,EAAE,sBAAsB,CAAC,SAAC;IACzC,CAAC;IAED,qBAAM,GAAN,UAAO,UAAkC,EAAE,SAAgB,EAAE,iBAAoC;QAAjG,iBA6BC;QA5BS,IAAA,SAAS,GAAoE,UAAU,UAA9E,EAAE,IAAI,GAA8D,UAAU,KAAxE,EAAE,OAAO,GAAqD,UAAU,QAA/D,EAAE,SAAS,GAA0C,UAAU,UAApD,EAAE,QAAQ,GAAgC,UAAU,SAA1C,EAAE,aAAa,GAAiB,UAAU,cAA3B,EAAE,UAAU,GAAK,UAAU,WAAf,CAAgB;QAChG,IAAM,WAAW,GAAG,QAAQ,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC,MAAM,CAAC,UAAC,EAAS;gBAAP,KAAK,WAAA;YAC/D,IAAI,UAAU,IAAI,KAAK,GAAG,UAAU,CAAC,CAAC,CAAC,IAAI,KAAK,GAAG,UAAU,CAAC,CAAC,CAAC;gBAAE,OAAO,KAAK,CAAC;YAC/E,OAAO,IAAI,CAAC;QACd,CAAC,CAAC,CAAC;QAEH,IAAM,cAAc,GAAG,oBAAoB,CAAC,iBAAiB,KAAK,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC;QAE3G,WAAW;QACX,IAAM,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,sBAAsB,CAAC,WAAW,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;QACvF,IAAM,eAAe,GACnB,MAAM,CAAC,QAAS,EAAE,SAAS,EAAE,UAAC,KAAK,IAAK,OAAA,UAAU,CAAC,KAAK,EAAE,WAAW,EAAE,UAAU,EAAE,cAAc,CAAC,EAA1D,CAA0D,CAAC,IAAI,EAAE,CAAC;QAE5G,iBAAiB;QACjB,IAAM,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,sBAAsB,CAAC,WAAW,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;QAEvF,IAAI,SAAS,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,IAAI,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,cAAc,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC;YAC9G,cAAc,CAAC,UAAU,EAAE,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE,WAAW,EAAE,oBAAoB,CAAC,KAAK,CAAC,CAAC,CAAC;QACpG,CAAC;QACD,SAAS;QACT,IAAM,eAAe,GAAG,cAAc,CAAC,UAAU,EAAE,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,EAAE,WAAW,EAAE,cAAc,CAAC,CAAC;QAC1G,YAAY;QACZ,IAAM,UAAU,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,sBAAsB,CAAC,WAAW,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;QACzF,IAAM,gBAAgB,GACpB,MAAM,CAAC,SAAS,EAAE,UAAU,EAAE,UAAC,KAAK;YAClC,OAAO,WAAW,CAAC,KAAK,EAAE,KAAI,EAAE,UAAU,EAAE,cAAc,CAAC,CAAC;QAC9D,CAAC,CAAC,IAAI,EAAE,CAAC;QACX,OAAO,qDAAI,eAAe,kBAAK,eAAe,kBAAK,gBAAgB,UAAE,IAAI,EAAE,CAAC,MAAM,CAAC,UAAC,CAAC,IAAK,OAAA,CAAC,CAAC,CAAC,EAAH,CAAG,CAAC,CAAC;IACjG,CAAC;IACH,WAAC;AAAD,CAAC,AAnCD,CAA0B,SAAS,GAmClC"}