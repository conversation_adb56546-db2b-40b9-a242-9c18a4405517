{"version": 3, "file": "d3-linear-nice.js", "sourceRoot": "src/", "sources": ["utils/d3-linear-nice.ts"], "names": [], "mappings": ";AAAA,uBAAuB;AACvB,iCAAiC;;;AAEjC,mCAAwC;AAGjC,MAAM,YAAY,GAAe,CAAC,GAAW,EAAE,GAAW,EAAE,QAAgB,CAAC,EAAE,EAAE;IACtF,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;IACrB,IAAI,EAAE,GAAG,CAAC,CAAC;IACX,IAAI,EAAE,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;IACtB,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IAClB,IAAI,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IACjB,IAAI,IAAI,CAAC;IAET,IAAI,IAAI,GAAG,KAAK,EAAE;QAChB,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAC9B,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;KACrB;IACD,IAAI,GAAG,IAAA,qBAAa,EAAC,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;IAEzC,IAAI,IAAI,GAAG,CAAC,EAAE;QACZ,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;QACxC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;QACrC,IAAI,GAAG,IAAA,qBAAa,EAAC,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;KAC1C;SAAM,IAAI,IAAI,GAAG,CAAC,EAAE;QACnB,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;QACvC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;QACtC,IAAI,GAAG,IAAA,qBAAa,EAAC,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;KAC1C;IAED,IAAI,IAAI,GAAG,CAAC,EAAE;QACZ,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;QACxC,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;KACvC;SAAM,IAAI,IAAI,GAAG,CAAC,EAAE;QACnB,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;QACvC,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;KACxC;IACD,OAAO,CAAC,CAAC;AACX,CAAC,CAAC;AAhCW,QAAA,YAAY,gBAgCvB"}