<h1 align="center">
<b>@antv/component</b>
</h1>

<div align="center">

Visualization components for AntV, based on [G](https://github.com/antvis/g) which is a flexible rendering engine for visualization.

[![build](https://github.com/antvis/component/actions/workflows/build.yml/badge.svg)](https://github.com/antvis/component/actions/workflows/build.yml)
[![Coverage Status](https://coveralls.io/repos/github/antvis/component/badge.svg?branch=master)](https://coveralls.io/github/antvis/component?branch=master)
[![npm Version](https://img.shields.io/npm/v/@antv/component.svg)](https://www.npmjs.com/package/@antv/component)
[![npm Download](https://img.shields.io/npm/dm/@antv/component.svg)](https://www.npmjs.com/package/@antv/component)
[![npm License](https://img.shields.io/npm/l/@antv/component.svg)](https://www.npmjs.com/package/@antv/component)

</div>


## ✨ Features

- **Rich** - Contains 20+ Visualization components, for AntV [G2](https://github.com/antvis/G2), [G6](https://github.com/antvis/G6), [L7](https://github.com/antvis/L7).
- **Powerful** - Each component has powerful abilities and flexible scalability.
- **Well Design** - Continuous optimization and iteration.
- **Powerful Renderer** - Based on the powerful renderer [G](https://github.com/antvis/G), we can render the components using `Canvas`, `SVG` or `WebGL` with same code.


## 📦 Installation

```bash
$ npm install @antv/component
```

```bash
$ yarn add @antv/component
```


## 🔨 Getting Started

```ts
import { Canvas } from '@antv/g';
import { Renderer } from '@antv/g-canvas';
import { Button } from '@antv/component';

// 1. New a canvas.
const canvas = new Canvas({
  container: 'container',
  width: 600,
  height: 600,
  renderer: new Renderer(),
});

// 2. Create a button with configure.
const button = new Button({
  /* ... */
});

// 3. Append into G canvas.
canvas.appendChild(button);

// 4. Render.
canvas.render();
```

## 📎 Documents

- [API](./docs/api.md)
- UI components
  - [Axis](./docs/components/axis.md)
  - [Legend](./docs/components/legend.md)
  - [Tooltip](./docs/components/tooltip.md)
  - [Slider](./docs/components/slider.md)
  - [Scrollbar](./docs/components/scrollbar.md)
  - [Button](./docs/components/button.md)
  - [Checkbox](./docs/components/checkbox.md)
  - [Navigator](./docs/components/navigator.md)
  - [Breadcrumb](./docs/components/breadcrumb.md)
  - [Sparkline](./docs/components/sparkline.md)


## 📮 Contribution

```bash
$ <NAME_EMAIL>:antvis/component.git

$ cd component

$ npm install

$ npm run dev
```

Then send a pull request on GitHub.

## 📄 License

MIT@[AntV](https://github.com/antvis).
