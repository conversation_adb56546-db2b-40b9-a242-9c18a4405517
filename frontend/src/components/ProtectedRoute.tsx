import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { Spin } from 'antd';
import { useAuth } from '../contexts/AuthContext';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: string;
  requiredPermission?: string;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredRole,
  requiredPermission
}) => {
  const { isAuthenticated, isLoading, hasRole, hasPermission, user } = useAuth();
  const location = useLocation();

  console.log('ProtectedRoute check:', { isAuthenticated, isLoading, user: user?.username });

  // E2E/dev bypass: if Playwright flag is set and L<PERSON> has token+user, allow render without redirect
  const isE2E = typeof window !== 'undefined' && (window as any).__PW_E2E === '1'
  const hasLSToken = typeof window !== 'undefined' && !!localStorage.getItem('access_token') && !!localStorage.getItem('user')
  if (isE2E && hasLSToken) {
    return <>{children}</>
  }

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        flexDirection: 'column'
      }}>
        <Spin size="large" tip="Checking authentication..." />
        <div style={{ marginTop: 16, color: '#666' }}>
          Please wait while we verify your session...
        </div>
      </div>
    );
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    console.log('User not authenticated, redirecting to login');
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // Check role requirement
  if (requiredRole && !hasRole(requiredRole)) {
    console.log(`User lacks required role: ${requiredRole}`);
    return <Navigate to="/unauthorized" replace />;
  }

  // Check permission requirement
  if (requiredPermission && !hasPermission(requiredPermission)) {
    console.log(`User lacks required permission: ${requiredPermission}`);
    return <Navigate to="/unauthorized" replace />;
  }

  console.log('ProtectedRoute: All checks passed, rendering children');
  // Render children if all checks pass
  return <>{children}</>;
};

export default ProtectedRoute;
