import { expect } from '@playwright/test'
import { test, ensureLoggedIn } from './fixtures/auth'

test('Journal Entries list: first column is JE ID', async ({ page }) => {
  await ensureLoggedIn(page)
  await page.goto('/finance/journal-entries')

  // Ensure page heading is visible (allow extra time on CI/dev)
  await expect(page.getByRole('heading', { name: 'Journal Entries' })).toBeVisible({ timeout: 20000 })

  // Check the first column header text without relying on ARIA role mapping
  await expect(page.getByText('Journal Entry ID')).toBeVisible({ timeout: 20000 })

  // Do not assume any rows exist in a clean DB; header presence is sufficient
})

