import axios from 'axios'
import { API_ENDPOINTS, API_TIMEOUT, DEFAULT_HEADERS } from '../config/api'

// Create axios instance
const apiClient = axios.create({
  baseURL: API_ENDPOINTS.BASE,
  timeout: API_TIMEOUT,
  headers: DEFAULT_HEADERS,
})

// Request interceptor
apiClient.interceptors.request.use(
  (config) => {
    // Add auth token if available (use correct key 'access_token')
    const token = localStorage.getItem('access_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// UX-01: Enhanced error handling with user-friendly messages
const getErrorMessage = (error: any): string => {
  // API provided error message
  if (error.response?.data?.detail) {
    return error.response.data.detail
  }
  
  // HTTP status code based messages
  switch (error.response?.status) {
    case 400:
      return 'Invalid request. Please check your input and try again.'
    case 401:
      return 'Session expired. Please log in again.'
    case 403:
      return 'You do not have permission to perform this action.'
    case 404:
      return 'The requested resource was not found.'
    case 409:
      return 'This action conflicts with existing data.'
    case 422:
      return 'Validation failed. Please check the highlighted fields.'
    case 500:
      return 'Server error. Please try again later.'
    case 502:
    case 503:
    case 504:
      return 'Service temporarily unavailable. Please try again in a few moments.'
    default:
      if (error.code === 'NETWORK_ERROR') {
        return 'Network connection failed. Please check your internet connection.'
      }
      if (error.code === 'ECONNABORTED') {
        return 'Request timed out. Please try again.'
      }
      return 'An unexpected error occurred. Please try again.'
  }
}

// UX-01: Retry mechanism for transient failures
const shouldRetry = (error: any): boolean => {
  const retryableCodes = [408, 429, 502, 503, 504]
  return retryableCodes.includes(error.response?.status) || 
         error.code === 'NETWORK_ERROR' ||
         error.code === 'ECONNABORTED'
}

let retryCount = 0
const MAX_RETRIES = 2

// Response interceptor with enhanced error handling
apiClient.interceptors.response.use(
  (response) => {
    retryCount = 0 // Reset retry count on successful response
    return response
  },
  async (error) => {
    const originalRequest = error.config

    if (error.response?.status === 401) {
      // Handle unauthorized access
      const suppressRedirect = (typeof window !== 'undefined') && (window as any).__PW_E2E === '1'
      if (!suppressRedirect) {
        // Production/normal behavior: clear and redirect to login
        localStorage.removeItem('access_token')
        localStorage.removeItem('user')
        delete apiClient.defaults.headers.common['Authorization']
        window.location.href = '/login'
      } else {
        // E2E/dev stabilization: do not clear session or redirect; let caller handle
        // This avoids ProtectedRoute bouncing to /login mid-test due to transient 401s on unmocked endpoints
        // Intentionally no-op here
      }
      return Promise.reject(error)
    }

    // UX-01: Implement retry mechanism for transient failures
    if (shouldRetry(error) && retryCount < MAX_RETRIES && !originalRequest._retry) {
      originalRequest._retry = true
      retryCount++
      
      // Wait before retrying (exponential backoff)
      const delay = Math.pow(2, retryCount) * 1000
      await new Promise(resolve => setTimeout(resolve, delay))
      
      return apiClient(originalRequest)
    }

    // Enhance error object with user-friendly message
    error.userMessage = getErrorMessage(error)
    retryCount = 0 // Reset retry count
    
    return Promise.reject(error)
  }
)

export default apiClient
