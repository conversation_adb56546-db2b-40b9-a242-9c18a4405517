import type { AxisBaseStyleProps } from './types';
export declare const AXIS_BASE_DEFAULT_ATTR: Partial<AxisBaseStyleProps>;
export declare const ARC_DEFAULT_OPTIONS: any;
export declare const HELIX_DEFAULT_OPTIONS: any;
export declare const CLASS_NAMES: {
    mainGroup: {
        name: string;
        class: string;
        id: string;
    };
    gridGroup: {
        name: string;
        class: string;
        id: string;
    };
    grid: {
        name: string;
        class: string;
        id: string;
    };
    lineGroup: {
        name: string;
        class: string;
        id: string;
    };
    line: {
        name: string;
        class: string;
        id: string;
    };
    tickGroup: {
        name: string;
        class: string;
        id: string;
    };
    tick: {
        name: string;
        class: string;
        id: string;
    };
    tickItem: {
        name: string;
        class: string;
        id: string;
    };
    labelGroup: {
        name: string;
        class: string;
        id: string;
    };
    label: {
        name: string;
        class: string;
        id: string;
    };
    labelItem: {
        name: string;
        class: string;
        id: string;
    };
    titleGroup: {
        name: string;
        class: string;
        id: string;
    };
    title: {
        name: string;
        class: string;
        id: string;
    };
    lineFirst: {
        name: string;
        class: string;
        id: string;
    };
    lineSecond: {
        name: string;
        class: string;
        id: string;
    };
};
