"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    default: function() {
        return _default;
    },
    pattersonRaw: function() {
        return pattersonRaw;
    }
});
var _index = require("../../d3-geo/src/index.js");
var _math = require("./math.js");
// Based on Java implementation by <PERSON><PERSON>.
// https://github.com/OSUCartography/JMapProjLib/blob/master/src/com/jhlabs/map/proj/PattersonProjection.java
var pattersonK1 = 1.0148, pattersonK2 = 0.23185, pattersonK3 = -0.14499, pattersonK4 = 0.02406, pattersonC1 = pattersonK1, pattersonC2 = 5 * pattersonK2, pattersonC3 = 7 * pattersonK3, pattersonC4 = 9 * pattersonK4, pattersonYmax = 1.790857183;
function pattersonRaw(lambda, phi) {
    var phi2 = phi * phi;
    return [
        lambda,
        phi * (pattersonK1 + phi2 * phi2 * (pattersonK2 + phi2 * (pattersonK3 + pattersonK4 * phi2)))
    ];
}
pattersonRaw.invert = function(x, y) {
    if (y > pattersonYmax) y = pattersonYmax;
    else if (y < -pattersonYmax) y = -pattersonYmax;
    var yc = y, delta;
    do {
        var y2 = yc * yc;
        yc -= delta = (yc * (pattersonK1 + y2 * y2 * (pattersonK2 + y2 * (pattersonK3 + pattersonK4 * y2))) - y) / (pattersonC1 + y2 * y2 * (pattersonC2 + y2 * (pattersonC3 + pattersonC4 * y2)));
    }while ((0, _math.abs)(delta) > _math.epsilon);
    return [
        x,
        yc
    ];
};
function _default() {
    return (0, _index.geoProjection)(pattersonRaw).scale(139.319);
}
