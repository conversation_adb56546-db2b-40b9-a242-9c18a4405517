import { expect } from '@playwright/test'
import { test, ensureLoggedIn } from './fixtures/auth'

function fmt(n: number) {
  return n.toFixed(2)
}

test('Sales form: calculation mode toggles fields and recalculates taxes (BC)', async ({ page }) => {
  await ensureLoggedIn(page)
  await page.goto('/finance/sales-invoices')

  // Start creating an Unpaid Sales invoice
  await page.getByTestId('btn-unpaid-sales').click()

  const invNumber = `UI-CALC-${Date.now()}`
  await page.getByLabel('Customer Name').fill('UI CalcCo')
  await page.getByRole('textbox', { name: /\* Invoice Number/i }).fill(invNumber)
  await page.getByRole('textbox', { name: /Invoice Date/i }).click()
  await page.keyboard.press('Enter')

  // Province default is BC; Calculation Mode default is From Subtotal
  // Enter subtotal and verify taxes and grand total calculated
  const subtotalInput = page.getByLabel('Subtotal (before taxes)')
  await subtotalInput.fill('100')

  const pstInput = page.getByLabel('PST Amount')
  const gstInput = page.getByLabel('GST/HST Amount')
  const grandTotalCalc = page.getByLabel('Grand Total (calculated)')

  // Wait for calculation to propagate
  await expect(pstInput).toHaveValue(fmt(7))
  await expect(gstInput).toHaveValue(fmt(5))
  await expect(grandTotalCalc).toHaveValue(fmt(112))

  // Switch to From Grand Total mode using the accessible combobox
  const modal2 = page.getByRole('dialog', { name: /create sales invoice|edit sales invoice/i })
  const calcModeCombo = modal2.getByRole('combobox', { name: 'Calculation Mode' })
  await calcModeCombo.click()
  const dropdown = page.locator('.ant-select-dropdown:visible')
  await dropdown.locator('.ant-select-item-option', { hasText: 'From Grand Total' }).click()

  // In grandtotal mode, fill Grand Total and verify back-calculated fields
  const grandTotalInput = page.getByRole('spinbutton', { name: /^\* Grand Total$/ })
  await grandTotalInput.fill('224')

  // Subtotal should be 200, PST 14, GST/HST 10 for BC
  const calcSubtotal = page.getByLabel('Subtotal (calculated)')
  await expect(calcSubtotal).toHaveValue(fmt(200))
  await expect(pstInput).toHaveValue(fmt(14))
  await expect(gstInput).toHaveValue(fmt(10))

  // Switch back to From Subtotal and ensure disabled/enabled states
  await calcModeFormItem.locator('.ant-select .ant-select-selector').first().click()
  const dropdown2 = page.locator('.ant-select-dropdown:visible')
  await dropdown2.locator('.ant-select-item-option', { hasText: 'From Subtotal' }).click()

  // Grand Total (calculated) should be disabled; Subtotal editable
  await expect(grandTotalCalc).toBeDisabled()
  await expect(subtotalInput).toBeEditable()

  // Select a revenue account (first option)
  const modal3 = page.getByRole('dialog', { name: /Create Sales Invoice|Edit Sales Invoice/i })
  const revCombo = modal3.getByRole('combobox', { name: 'Revenue Account (Credit)' })
  const { selectFirstOptionForCombobox } = await import('./utils/select')
  await selectFirstOptionForCombobox(page, revCombo, 8000)

  // Submit (OK) and wait for modal to close deterministically
  await page.getByRole('button', { name: /^OK$|^Create|^Save/i }).first().click()
  await page.getByRole('dialog', { name: /Create Sales Invoice|Edit Sales Invoice/i }).waitFor({ state: 'hidden', timeout: 15000 })
})

