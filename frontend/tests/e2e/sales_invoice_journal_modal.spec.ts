import { expect } from '@playwright/test'
import { test } from './fixtures/auth'

async function ensureLoggedIn(page) {
  // Go directly to Sales Invoices; if login shows, sign in then retry
  await page.goto('/finance/sales-invoices')
  if (page.url().includes('/login')) {
    await page.getByPlaceholder('Username').fill('admin')
    await page.getByPlaceholder('Password').fill('admin123')
    await page.getByRole('button', { name: /sign in/i }).click()
    await page.goto('/finance/sales-invoices')
  }
  await page.locator('[data-testid="btn-unpaid-sales"]').waitFor({ state: 'visible', timeout: 20000 })
}

test('Sales Invoice: JE modal renders without console errors', async ({ page }) => {
  test.setTimeout(90_000)
  const errors: string[] = []
  page.on('console', (msg) => {
    if (msg.type() === 'error') errors.push(msg.text())
  })

  // Create via UI
  await ensureLoggedIn(page)
  await expect(page.getByRole('heading', { name: 'Revenue (Sales)' })).toBeVisible({ timeout: 20000 })
  await page.locator('[data-testid="btn-unpaid-sales"]').click()
  const invNumber = `INV-E2E-${Date.now()}`
  await page.getByLabel('Customer Name').fill('ACME Corp')
  await page.getByRole('textbox', { name: /\* Invoice Number/i }).fill(invNumber)
  await page.getByRole('textbox', { name: /\* Invoice Date/i }).click()
  await page.keyboard.press('Enter')
  await page.getByRole('spinbutton', { name: /Subtotal/i }).fill('100')
  const revCombo = page.getByRole('combobox', { name: 'Revenue Account (Credit)' })
  const { selectFirstOptionForCombobox } = await import('./utils/select')
  await selectFirstOptionForCombobox(page, revCombo, 8000)
  await page.getByRole('button', { name: /^ok$/i }).click()
  // Wait for modal to close deterministically
  await page.getByRole('dialog', { name: /create sales invoice|edit sales invoice/i }).waitFor({ state: 'hidden', timeout: 15000 })
  // Advanced Filters: deterministically expand before interacting
  await page.keyboard.press('Escape').catch(() => {})
  const summaryPanel = page.getByTestId('filters-panel-sales-invoices-summary')
  const panel = page.getByTestId('filters-panel-sales-invoices-content')
  if (await summaryPanel.isVisible().catch(() => false) || !(await panel.isVisible().catch(() => false))) {
    await page.getByTestId('filters-toggle-sales-invoices').click({ force: true })
  }
  await expect(panel).toHaveAttribute('data-expanded', 'true', { timeout: 10000 })

  await page.locator('.ant-select-dropdown:visible').first().waitFor({ state: 'hidden', timeout: 1500 }).catch(() => {})
  // Scroll to top so filters card is in view
  await page.evaluate(() => window.scrollTo(0, 0))
  // Panel reference already defined above as `panel`; ensure expanded deterministically
  if (await panel.getAttribute('data-expanded') !== 'true') {
    await page.getByTestId('filters-toggle-sales-invoices').click({ force: true })
    await expect(panel).toHaveAttribute('data-expanded', 'true', { timeout: 10000 })
  }
  // Wait for Invoice Number field to appear and fill it via test id
  const invInput = page.getByTestId('filter-input-sales-invoices-invoice_number')
  await invInput.waitFor({ state: 'visible', timeout: 10000 })
  await invInput.fill(invNumber)
  const row = page.locator('tr').filter({ hasText: invNumber }).first()
  await expect(row).toBeVisible({ timeout: 20000 })
  await expect(row).toBeVisible({ timeout: 20000 })
  await row.getByRole('button', { name: /^submit$/i }).click()
  await page.getByRole('button', { name: /^ok$/i }).click()
  await expect(page.getByText(/sales invoice submitted successfully/i)).toBeVisible({ timeout: 20000 })

  // View JE; generate if not exists (use the same row)
  await page.locator(`[data-testid="btn-view-je-${rowId}"]`).click()
  const noJeDialog = page.getByRole('dialog', { name: /no journal entry found/i })
  if (await noJeDialog.isVisible({ timeout: 2000 }).catch(() => false)) {
    await page.getByRole('button', { name: /^ok$/i }).click() // confirm generate
    await page.locator(`[data-testid=\"btn-view-je-${rowId}\"]`).click()
  }

  await expect(page.getByText(/Sales Invoice Journal Entry/i)).toBeVisible({ timeout: 15000 })
  await expect(page.getByText(/Journal Entry #/i)).toBeVisible()
  await expect(page.getByText(/Sales Invoice Details/i)).toBeVisible()

  const typeErrors = errors.filter(e => /TypeError/i.test(e))
  expect(typeErrors).toHaveLength(0)
})

test('Sales Invoice: Edit draft and modal closes on OK', async ({ page }) => {
  // Create draft via UI
  await ensureLoggedIn(page)
  await page.locator('[data-testid="btn-unpaid-sales"]').click()
  const invNumber = `INV-DRAFT-${Date.now()}`
  await page.getByLabel('Customer Name').fill('Draft Customer')
  await page.getByRole('textbox', { name: /\* Invoice Number/i }).fill(invNumber)
  await page.getByRole('textbox', { name: /\* Invoice Date/i }).click()
  await page.keyboard.press('Enter')
  await page.getByRole('spinbutton', { name: /Subtotal/i }).fill('50')
  const revCombo = page.getByRole('combobox', { name: 'Revenue Account (Credit)' })
  await revCombo.click()
  const dropdown = page.locator('.ant-select-dropdown:visible')
  await dropdown.waitFor({ state: 'visible', timeout: 10000 })
  const firstOption2 = dropdown.locator('.ant-select-item-option').first()
  if (await firstOption2.isVisible().catch(() => false)) {
    await firstOption2.click()
  } else {
    await page.keyboard.press('ArrowDown')
    await page.keyboard.press('Enter')
  }
  await page.getByRole('button', { name: /^ok$/i }).click()
  await page.getByRole('dialog', { name: /create sales invoice/i }).waitFor({ state: 'hidden', timeout: 15000 }).catch(() => {})
  // Expand filters again if collapsed
  const filtersCard2 = page.locator('.ant-card:has-text("Advanced Filters")').first()
  const expandBtn2 = filtersCard2.getByRole('button', { name: /filters|collapse/i })
  if (await expandBtn2.isVisible().catch(() => false)) {
    await expandBtn2.click()
  }
  await page.keyboard.press('Escape').catch(() => {})
  await page.locator('.ant-select-dropdown:visible').first().waitFor({ state: 'hidden', timeout: 1500 }).catch(() => {})
  await page.evaluate(() => window.scrollTo(0, 0))
  // Ensure filters panel is open (idempotent)
  const summary2 = page.getByTestId('filters-panel-sales-invoices-summary')
  const panel2 = page.getByTestId('filters-panel-sales-invoices-content')
  if (await summary2.isVisible().catch(() => false) || !(await panel2.isVisible().catch(() => false))) {
    await page.getByTestId('filters-toggle-sales-invoices').click({ force: true })
  }
  await expect(panel2).toHaveAttribute('data-expanded', 'true', { timeout: 10000 })
  const invInput2 = page.getByTestId('filter-input-sales-invoices-invoice_number')
  await invInput2.waitFor({ state: 'visible', timeout: 10000 })
  await invInput2.fill(invNumber)
  const draftRow = page.locator('tr').filter({ hasText: invNumber }).first()
  await expect(draftRow).toBeVisible({ timeout: 20000 })

  // Edit the specific draft row
  await draftRow.getByRole('button', { name: /^edit$/i }).click()
  await page.getByLabel('Customer Name').fill('Updated Customer')
  await page.getByRole('button', { name: /^ok$/i }).click()

  await expect(page.getByRole('dialog', { name: /edit sales invoice|create sales invoice/i })).toBeHidden()
})

