{"name": "@antv/coord", "version": "0.4.7", "description": "<PERSON>lk<PERSON> for mapping elements of sets into geometric objects.", "license": "MIT", "main": "lib/index.js", "module": "esm/index.js", "browser": "dist/coordinate.min.js", "types": "lib/index.d.ts", "files": ["src", "lib", "esm", "dist"], "scripts": {"clean": "<PERSON><PERSON>f lib esm dist", "lint-staged": "lint-staged", "size": "limit-size", "lint": "eslint ./src ./__tests__ && prettier ./src ./__tests__ --check ", "fix": "eslint ./src ./__tests__ --fix && prettier ./src ./__tests__ --write ", "test": "jest", "build:umd": "rimraf ./dist && rollup -c && npm run size", "build:cjs": "rimraf ./lib && tsc --module commonjs --outDir lib", "build:esm": "rimraf ./esm && tsc --module ESNext --outDir esm", "build": "run-p build:*", "ci": "run-s lint test build", "prepublishOnly": "npm run ci", "prepare": "husky install"}, "dependencies": {"gl-matrix": "^3.4.3", "@antv/scale": "^0.4.12", "@antv/util": "^2.0.13"}, "devDependencies": {"@commitlint/cli": "^11.0.0", "@rollup/plugin-commonjs": "^20.0.0", "@types/gl-matrix": "^2.4.5", "@types/jest": "^26.0.20", "@typescript-eslint/eslint-plugin": "^4.18.0", "@typescript-eslint/parser": "^4.18.0", "eslint": "^7.22.0", "eslint-plugin-import": "^2.22.1", "husky": "^6.0.0", "jest": "^26.6.3", "limit-size": "^0.1.4", "lint-staged": "^10.5.4", "npm-run-all": "^4.1.5", "prettier": "^2.2.1", "rimraf": "^3.0.2", "rollup": "^2.39.0", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-typescript": "^1.0.1", "rollup-plugin-uglify": "^6.0.4", "ts-jest": "^26.5.1", "typescript": "4.8.3"}, "jest": {"preset": "ts-jest", "collectCoverage": true, "testRegex": "(/__tests__/.*\\.(test|spec))\\.ts$", "collectCoverageFrom": ["src/**/*.ts"]}, "lint-staged": {"*.{ts,tsx}": ["eslint --fix", "prettier --write", "git add"]}, "limit-size": [{"path": "dist/coordinate.min.js", "limit": "12 Kb", "gzip": true}, {"path": "dist/coordinate.min.js", "limit": "40 Kb"}], "husky": {"hooks": {"pre-commit": "npm run lint-staged", "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "author": {"name": "AntV", "url": "https://antv.vision/"}, "repository": {"type": "git", "url": "https://github.com/antvis/coord"}, "bugs": {"url": "https://github.com/antvis/coord/issues"}, "keywords": ["coordinate", "transformation", "visualization"]}