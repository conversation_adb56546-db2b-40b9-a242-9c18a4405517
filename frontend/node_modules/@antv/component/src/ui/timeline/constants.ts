// import { TimelineStyleProps } from './types';

// const DEFAULT_BUTTON_STYLE = {
//   margin: [2, 4],
//   markerStyle: {
//     stroke: '#bfbfbf',
//     active: {
//       stroke: '#3471F9',
//     },
//   },
//   backgroundStyle: {
//     fill: 'transparent',
//   },
// };

// export const DEFAULT_TIMELINE_STYLE: TimelineStyleProps = {
//   x: 0,
//   y: 0,
//   data: [],
//   width: 500,
//   height: 40,
//   selection: [0, 0],
//   orientation: 'horizontal',
//   singleModeControl: {
//     width: 56,
//     size: 12,
//     labelStyle: {
//       text: '单一时间',
//       fontSize: 10,
//       dx: 4,
//     },
//   },
//   speedControl: {
//     width: 32,
//     markerSize: 4,
//     speeds: [1.0, 2.0, 3.0, 4.0, 5.0],
//     labelStyle: {
//       fontFamily: 'sans-serif',
//       fill: 'rgba(0,0,0,0.45)',
//       fontStyle: 'normal',
//       fontWeight: 500,
//       fontSize: 10,
//       textBaseline: 'top',
//     },
//     formatter: (value: number) => `${value.toFixed(1)}x`,
//   },
//   controlPosition: 'bottom',
//   controlButton: {
//     spacing: 14,
//     prevBtn: {
//       ...DEFAULT_BUTTON_STYLE,
//       symbol: 'timeline-prev-btn',
//       padding: 0,
//       size: 8,
//     },
//     nextBtn: {
//       ...DEFAULT_BUTTON_STYLE,
//       symbol: 'timeline-next-btn',
//       padding: 0,
//       size: 8,
//     },
//     playBtn: {
//       margin: 4,
//       padding: 0,
//       size: 20,
//       symbol: '',
//       markerStyle: {
//         stroke: '#bfbfbf',
//         fill: '#bfbfbf',
//         active: {
//           stroke: '#3471F9',
//           fill: '#3471F9',
//         },
//       },
//       backgroundStyle: {
//         stroke: '#bfbfbf',
//         lineWidth: 1,
//         fill: '#F7F7F7',
//         active: {
//           fill: 'rgba(52, 113, 249, 0.1)',
//           stroke: '#3471F9',
//         },
//       },
//     },
//   },
//   padding: [0, 8],
//   playAxis: {
//     appendPadding: [4, 0],
//     handleStyle: {
//       r: 3,
//       lineWidth: 1,
//     },
//     label: {
//       position: 1,
//       tickPadding: 2,
//       tickLine: {
//         len: 4,
//         style: {
//           stroke: 'rgba(0,0,0,0.25)',
//           lineWidth: 1,
//         },
//       },
//       style: {
//         fontSize: 10,
//         fillOpacity: 1,
//         fill: 'rgba(0,0,0,0.45)',
//       },
//     },
//     loop: false,
//   },
//   playInterval: 2000,
//   autoPlay: false,
// };
