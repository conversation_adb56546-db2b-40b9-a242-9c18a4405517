import { test as base } from '@playwright/test'
import axios from 'axios'

const consoleLogs = new WeakMap<import('@playwright/test').Page, string[]>()

const sleep = (ms: number) => new Promise(res => setTimeout(res, ms))

async function loginWithRetry(maxAttempts = 10, delayMs = 500) {
  let token = 'test-token'
  let user: any = { id: 1, username: 'admin', email: '<EMAIL>', full_name: 'Admin', is_superuser: true, roles: ['admin'] }
  for (let i = 0; i < maxAttempts; i++) {
    try {
      const resp = await axios.post('http://localhost:8000/api/v1/auth/login', {
        username: 'admin',
        password: 'admin123',
      }, { timeout: 3000 })
      token = resp.data?.access_token || token
      user = resp.data?.user || user
      return { token, user, success: true }
    } catch (e) {
      await sleep(delayMs)
    }
  }
  return { token, user, success: false }
}

async function waitForStable(page: import('@playwright/test').Page) {
  const url = page.url()
  const timeout = 10000
  try {
    // E2E: Removed UI login fallback; rely solely on token injection and /auth/me mock

    // If currently on login, do not block; token injection + /auth/me mock will flip state
    if (/\/login$/.test(url)) {
      return
    }

    // First, try to ensure main layout is rendered (robust across pages)
    await page.locator('.app-layout').waitFor({ state: 'visible', timeout }).catch(() => {})

    if (/\/finance\/journal-entries/.test(url)) {
      await page.getByRole('heading', { name: 'Journal Entries' }).waitFor({ state: 'visible', timeout })
      await page.locator('.ant-table thead').first().waitFor({ state: 'visible', timeout })
      await page.getByText('Journal Entry ID').first().waitFor({ state: 'visible', timeout })
    } else if (/\/finance\/sales-invoices/.test(url)) {
      await page.getByRole('heading', { name: 'Revenue (Sales)' }).waitFor({ state: 'visible', timeout })
      await page.locator('.table-actions').first().waitFor({ state: 'visible', timeout })
      await page.getByTestId('btn-unpaid-sales').waitFor({ state: 'visible', timeout })
    } else if (/\/finance\/purchase-invoices/.test(url)) {
      await page.getByRole('heading', { name: 'Expenses' }).waitFor({ state: 'visible', timeout })
      await page.locator('.table-actions').first().waitFor({ state: 'visible', timeout })
    } else if (/\/report-generator/.test(url)) {
      await page.getByRole('heading', { name: /report generator/i }).waitFor({ state: 'visible', timeout })
      await page.getByRole('tab', { name: /generate/i }).waitFor({ state: 'visible', timeout }).catch(() => {})
    } else {
      // Generic readiness (non-blocking in E2E)
      await page.locator('.app-layout').waitFor({ state: 'visible', timeout }).catch(() => {})
    }
  } catch (e) {
    // Non-fatal: tests will still try their own waits/assertions
  }
}

// Custom test with authenticated context and navigation stabilization
export const test = base.extend<{}>({
  context: async ({ context }, use) => {
    // Capture console logs for diagnostics
    context.on('page', (page) => {
      const logs: string[] = []
      consoleLogs.set(page, logs)
      page.on('console', (msg) => logs.push(`[${msg.type()}] ${msg.text()}`))
    })

    // Stabilize authentication by mocking /auth/me to return valid session
    await context.route('**/api/v1/auth/me', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          id: 1,
          username: 'admin',
          email: '<EMAIL>',
          full_name: 'Admin',
          is_superuser: true,
          roles: ['admin']
        })
      })
    })

    let token = 'test-token'
    let user: any = { id: 1, username: 'admin', email: '<EMAIL>', full_name: 'Admin', is_superuser: true, roles: ['admin'] }

    // Try real backend login first; fall back to mock token/user if unavailable
    try {
      const lr = await loginWithRetry(12, 500)
      token = lr.token
      user = lr.user
    } catch {
      // Fallback to mock auth for fully mocked e2e runs
    }

    // Inject auth and E2E flag into localStorage/window for all new pages before scripts run
    await context.addInitScript((tk, usr) => {
      (window as any).__PW_E2E = '1'
      window.localStorage.setItem('access_token', tk as string)
      window.localStorage.setItem('user', JSON.stringify(usr))
    }, token, user)

    await use(context)
  },

  page: async ({ page }, use) => {
    // Wrap page.goto to wait for a stable signal after navigation
    const origGoto = page.goto.bind(page)
    page.goto = (async (url: string, options?: any) => {
      const opts = { ...(options || {}), waitUntil: options?.waitUntil ?? 'domcontentloaded' }
      const res = await origGoto(url, opts)
      // E2E: Removed UI login fallback; rely on token injection and /auth/me mock to prevent redirects
      await waitForStable(page)
      return res
    }) as any

    // Also wrap reload
    const origReload = page.reload.bind(page)
    page.reload = (async (options?: any) => {
      const opts = { ...(options || {}), waitUntil: options?.waitUntil ?? 'domcontentloaded' }
      const res = await origReload(opts)
      await waitForStable(page)
      return res
    }) as any

    await use(page)
  },

  // Attach page HTML on failure for diagnostics
  // Note: Only applies to specs importing this custom test
  // For specs using base test directly, consider migrating them to this fixture
  // to inherit this behavior.
  // eslint-disable-next-line @typescript-eslint/no-misused-promises
  afterEach: async ({ page }, testInfo) => {
    if (testInfo.status !== testInfo.expectedStatus) {
      try {
        const html = await page.content()
        await testInfo.attach('page-content.html', { body: html, contentType: 'text/html' })
      } catch {}
    }
  },
})

// Global beforeEach to ensure app shell is ready for every test
// Navigates to base and waits for main layout, preventing early selector timeouts
// eslint-disable-next-line @typescript-eslint/no-misused-promises
test.beforeEach(async ({ page }) => {
  const baseUrl = process.env.PLAYWRIGHT_BASE_URL || 'http://localhost:3000'
  if (page.url() === 'about:blank') {
    await page.goto(baseUrl)
  }
  await waitForStable(page)
})

export const expect = base.expect



// Helper to ensure login state on an existing page (idempotent)
// Tries real backend login; falls back to mock token/user
export async function ensureLoggedIn(page: import('@playwright/test').Page) {
  let token = 'test-token'
  let user: any = { id: 1, username: 'admin', email: '<EMAIL>', full_name: 'Admin', is_superuser: true, roles: ['admin'] }
  try {
    const lr = await loginWithRetry(12, 500)
    token = lr.token
    user = lr.user
  } catch {}
  // Ensure we have an origin before touching localStorage
  if (page.url() === 'about:blank') {
    const base = process.env.PLAYWRIGHT_BASE_URL || 'http://localhost:3000'
    await page.goto(base)
  }
  await page.evaluate(({ tk, usr }) => {
    ;(window as any).__PW_E2E = '1'
    window.localStorage.setItem('access_token', tk as string)
    window.localStorage.setItem('user', JSON.stringify(usr))
  }, { tk: token, usr: user })
  // Reload so AuthContext initializes with the token, but do not force UI login in E2E
  await page.reload({ waitUntil: 'domcontentloaded' })
  await waitForStable(page)
}
