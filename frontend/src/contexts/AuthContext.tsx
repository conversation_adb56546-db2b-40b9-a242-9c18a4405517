import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import apiClient from '../api/client';

interface User {
  id: number;
  username: string;
  email: string;
  full_name: string;
  is_superuser: boolean;
  roles: string[];
}

interface AuthContextType {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (username: string, password: string) => Promise<void>;
  logout: () => void;
  hasRole: (role: string) => boolean;
  hasPermission: (permission: string) => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Initialize auth state from localStorage
  useEffect(() => {
    const isDev = ['3000', '3001', '5173', '5174'].includes(window.location.port)

    const initializeAuth = async () => {
      try {
        const storedToken = localStorage.getItem('access_token');
        const storedUser = localStorage.getItem('user');

        if (storedToken && storedUser) {
          const parsedUser = JSON.parse(storedUser);

          if (isDev) {
            // Dev-only: optimistically authenticate to avoid redirects/spinners.
            setToken(storedToken);
            setUser(parsedUser);
            setIsLoading(false);
            console.log('Dev auth: using localStorage token; validating in background');

            // Validate token in the background; do not force logout on failure in dev
            try {
              await apiClient.get('/auth/me');
              console.log('Dev auth: token validated');
            } catch (tokenError) {
              console.warn('Dev auth: token validation failed; keeping session for dev/E2E');
            }
            return; // Already set loading false for dev path
          }

          // Production/Non-dev: validate token before restoring session
          try {
            // The token will be added by the apiClient interceptor
            await apiClient.get('/auth/me');

            // Token is valid, set auth state
            setToken(storedToken);
            setUser(parsedUser);
            console.log('Authentication restored from localStorage');
          } catch (tokenError) {
            console.warn('Stored token is invalid, clearing auth data');
            // Token is invalid, clear everything
            localStorage.removeItem('access_token');
            localStorage.removeItem('user');
          }
        }
      } catch (error) {
        console.error('Error initializing auth:', error);
        // Clear invalid data
        localStorage.removeItem('access_token');
        localStorage.removeItem('user');
      } finally {
        if (!isDev) {
          setIsLoading(false);
        }
        // In dev, loading was already set false in the optimistic branch; if we didn't have a token at all, set it here
        if (isDev) {
          setIsLoading(false);
        }
      }
    };

    initializeAuth();
  }, []);

  const login = async (username: string, password: string): Promise<void> => {
    try {
      console.log('Attempting login for user:', username);

      const response = await apiClient.post('/auth/login', {
        username,
        password
      });

      const { access_token, user: userData } = response.data;

      console.log('Login successful, user data:', userData);

      // Store in state first
      setToken(access_token);
      setUser(userData);

      // Store in localStorage
      localStorage.setItem('access_token', access_token);
      localStorage.setItem('user', JSON.stringify(userData));

      console.log('Authentication state updated successfully');
    } catch (error) {
      console.error('Login error:', error);
      // Clear any partial state
      setToken(null);
      setUser(null);
      localStorage.removeItem('access_token');
      localStorage.removeItem('user');
      throw error;
    }
  };

  const logout = () => {
    // Clear state
    setToken(null);
    setUser(null);

    // Clear localStorage
    localStorage.removeItem('access_token');
    localStorage.removeItem('user');
  };

  const hasRole = (role: string): boolean => {
    // Superusers have all roles
    if (user?.is_superuser) {
      return true;
    }
    return user?.roles?.includes(role) || false;
  };

  const hasPermission = (_permission: string): boolean => {
    // For now, superusers have all permissions
    if (user?.is_superuser) {
      return true;
    }

    // TODO: Implement granular permission checking
    // This would require fetching user permissions from the backend
    return false;
  };

  const value: AuthContextType = {
    user,
    token,
    isAuthenticated: !!token && !!user,
    isLoading,
    login,
    logout,
    hasRole,
    hasPermission
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default AuthContext;
